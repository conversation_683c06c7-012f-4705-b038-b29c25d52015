<?php

namespace App\Services\MailSend;

use App\Model\CronsetupTimes;
use App\Model\EmailPlaceholder;
use App\Services\Mail\MailService;
use App\Services\MailSend\Interfaces\MailSendInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class MailSendService implements MailSendInterface
{

    function mail_send_by_IDS($timeIds,$resend = false){
        if($resend){
            try {
                for($i = 0;$i < count($timeIds);$i++){
                    $result[] = $this->resendTreatMailById($timeIds[$i]);
                }
            } catch (\Throwable $th) {
                $result[] = $th;
            }
            return $result;

        }else return $result[] = $this->CronMailSend($timeIds);
    }

    /**
     * Do Cron Mail Send
    */

    public function CronMailSend($ids)
    {
        #default_timezone
        date_default_timezone_set('Europe/Vienna');

        $crontime = DB::table("cronsetup_times")->whereIn('id',$ids)->get();

        #email placeholder
        if(Cache::has('defaultPlaceHolder')){
            $defaultPlaceHolder = Cache::get('defaultPlaceHolder');
        }else{
            $defaultPlaceHolder = EmailPlaceholder::get();
            Cache::forever('defaultPlaceHolder',$defaultPlaceHolder);
        }
        $mailreport = array();
        $send_status = false;

        foreach($crontime as $ctkey => $ctvalues)
        {
            $time_start = microtime(true);

            ### QUERY START ###

            #alltimes
            $crontimes = DB::table("cronsetup_times")
                        ->select("id", "end_time")->where("status", "!=", 2)
                        ->where("cronsetup_id", $ctvalues->cronsetup_id)->get();

            $timescnt = count($crontimes);

            #admin info
            $admin  = DB::table("cron_setups")
                        ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cron_setups.id")
                        ->join("cron_settings", "cron_setups.user_id", "=", "cron_settings.user_id")
                        ->join("users", "users.id", "=", "cron_setups.user_id")
                        ->join("smtps", "smtps.user_id", "=", "cron_setups.user_id")
                        // ->join("email_templates", "email_templates.cron_setting_id", "=", "cron_settings.id")
                        ->select(
                                'cron_setups.*',
                                'cronsetup_options.*',
                                'cron_settings.id', 'cron_settings.send_email_time',
                                'users.first_name', 'users.last_name', 'users.cron_email', 'users.user_status', 'users.language_id',
                                'smtps.*'
                                // 'email_templates.*'
                        )->where("cron_setups.id", $ctvalues->cronsetup_id)->first();

            #subuser info
            $subuser  = DB::table("users")->select("first_name", "language_id", "last_name", "cron_email", "user_status")->where("id", $admin->selected_user)->first();

            #analyses info
            $analyses = DB::table("cronsetup_analyses")
                            ->where("start_time", $ctvalues->start_time)
                            ->where("cron_setup_id", $ctvalues->cronsetup_id)->first();
            $lang = 'de';
            if($subuser && $subuser->language_id != '2') $lang = DB::table('languages')->find($subuser->language_id)->short_code;
            setServerLocal($lang);

            $tableName = ($lang != 'de')? $lang.'_email_templates' : 'email_templates';
            $adminTemplate = DB::table($tableName)->where('cron_setting_id',$admin->cronsettingid)->first();

            ### QUERY END ###

            #before send time

	        // $beforesend = 300;
	        // if($admin->send_email_time != "" || $admin->send_email_time != 0)
	        // 	$beforesend = $admin->send_email_time*60;

            #time info
	        $start_time = strtotime($ctvalues->start_time);
            $end_time   = $crontimes[$timescnt-1]->end_time;
            $nowtime    = strtotime(date('Y-m-d H:i:s'));
            $endtime    = strtotime($end_time);
	        $diff = $start_time - $nowtime;
	        $end_diff = $endtime - $nowtime;


            $sender_name = $admin->fullName;
            #reciver info
			$receiver_email = ($admin->email == null)? $subuser->cron_email : $admin->email;
            $reciver_name = $subuser->fullName;

            #conditions to ignore sendmail
            // if($diff > $beforesend) continue;
            // if($diff < 0 && $end_diff > 120) continue;
            if($admin->user_status == 0 || $subuser->user_status == 0 || $admin->is_deleted == 1 || $admin->as_draft == 1 || $admin->is_stop == 1 && $receiver_email == '') continue;
	        $beforesend =  Carbon::parse($admin->end_email_time)->greaterThan(Carbon::now());
            if(!$beforesend){
                //cronmail record
                $mailquery = [
                    'admin_id' => $admin->user_id,
                    'user_id' => $admin->selected_user,
                    'user_email' => ($admin->email == null)? $subuser->cron_email : $admin->email,
                    'execution_time' => round($time_start, 3),
                    'status' => 'Failed',
                    'mail_type' => 2,
                    'created_at' => Carbon::now()
                ];
                DB::table("cronmail_record")->insert($mailquery);
                continue;
            }
            #1-start 2-end 0-red
            $mailType = '';
            if(($end_diff > 0 && $end_diff <= 120) && ($ctvalues->status == 0 || $ctvalues->status == 1) && (($admin->due_power == 0 && $admin->end_email == 1) || ($admin->due_power == 1 && $admin->end_email_duepower == 1))){
                $mailType = 2;
            }else if(!$analyses){
                $mailType = 0;
            }
            else if($beforesend && $ctvalues->status == 0 && (($admin->due_power == 0 && $admin->start_email == 1) || ($admin->due_power == 1 && $admin->start_email_duepower == 1))){
                $mailType = 1;
            }

            #smtp configuration
            $smtp_config = $this->smtpConfig($admin, $sender_name);

            #PDF link setup
	        $pdf_status = false;
            $pdflink = "";
            $click = trans('action.click_here');

	        if($admin->pdf_export == 1)
	        {
	        	$pdflink = url('/treatment/view_pdf',[$admin->selected_user, $ctvalues->cronsetup_id, $ctvalues->id, $admin->unique_id]);
	        	$pdf_status = true;
            }

            if(($end_diff > 0 && $end_diff < 120) || !$analyses) $pdf_status = false;

            #array in for checking condition
            $others = [
                'diff' => $diff,
                'end_diff' => $end_diff,
                'before' => $beforesend,
                'start_time' => $ctvalues->start_time,
                'end_time' => $ctvalues->end_time,
                'nowtime' => $nowtime,
                'status' => $ctvalues->status,
                'cronsetup_id' => $ctvalues->cronsetup_id,
                'crontime_id' => $ctvalues->id,
                'click' => $click,
                'pdf_link' => $pdflink
            ];

            #emailtemplate setup
            $emailTemplate = $this->EmailTemplate($admin, $analyses, $mailType, $adminTemplate, $defaultPlaceHolder, $lang);

            #template structure converated
            $templateStucture = $this->mailTamplate($emailTemplate['temp_body'], $subuser, $admin, $others);

            #mail template array
            $mailTamplate = ['mail' => true, 'mail_subject' => $emailTemplate['temp_title'], 'mail_body' => $templateStucture, 'mail_footer' => $emailTemplate['temp_footer'], "pdf_status" => $pdf_status, "pdf" => $pdflink];
            if($receiver_email != "" && $mailType == 1){
                $recipient['mail'] = $receiver_email;
                $recipient['name'] = $reciver_name;
                $datetime = date("Y-m-d H:i:s");

                $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate,$lang);

                $time_end = microtime(true);
                $ex_time = ($time_end - $time_start)/60;
                $runtime = round($ex_time, 3);

                $mailstatus = 1;
                if($crontimes[$timescnt-1]->id != $ctvalues->id) $mailstatus = 2;

                #updating cronsetuptime/treatment_time status
                $statusUpdate = CronsetupTimes::find($ctvalues->id);
                $statusUpdate->update(array('status' => $mailstatus));

                #cronmail record
                $mailquery = [
                    'admin_id' => $admin->user_id,
                    'user_id' => $admin->selected_user,
                    'user_email' => $receiver_email,
                    'execution_time' => $runtime,
                    'status' => $mailsend_status,
                    'mail_type' => 0,
                    'created_at' => $datetime
                ];

                $mailreport[] = $mailquery;
                $send_status = true;
            }else if($receiver_email != "" && $mailType == 2){

                $recipient['mail'] = $receiver_email;
                $recipient['name'] = $reciver_name;
                $datetime = date("Y-m-d H:i:s");

                $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate,$lang);

                $time_end = microtime(true);
                $ex_time = ($time_end - $time_start)/60;
                $runtime = round($ex_time, 2);

                #updating cronsetuptime/treatment_time status
                $statusUpdate = CronsetupTimes::find($ctvalues->id);
                $statusUpdate->update(array('status' => 2));

                //cronmail record
                $mailquery = [
                    'admin_id' => $admin->user_id,
                    'user_id' => $admin->selected_user,
                    'user_email' => $receiver_email,
                    'execution_time' => $runtime,
                    'status' => $mailsend_status,
                    'mail_type' => 1,
                    'created_at' => $datetime
                ];

                $mailreport[] = $mailquery;
                $send_status = true;
            }
        }

        #storing mail report
        if($send_status == true)
        {
            DB::table("cronmail_record")->insert($mailreport);
        }
        return $mailreport;
    }

    /**
     * Send Mail to the receiver
     * @param ids @param type
     * @return mail_send_response
     */
    public function resendTreatMailById($crontime_id)
    {
        $crontime = DB::table("cronsetup_times")->find($crontime_id);

        $admin  = DB::table("cron_setups")#sender
                        ->select(
                            'cron_setups.*','cron_setups.email as receiver_email',
                            'cronsetup_options.*',
                            'cron_settings.id','cron_settings.id as cronsettingid','cron_settings.send_email_time',
                            DB::raw("CONCAT(first_name, ' ', last_name) AS fullName"), 'users.first_name', 'users.last_name', 'users.cron_email','users.user_status',"users.language_id",
                            'smtps.*'
                        )
                        ->join("cronsetup_options", "cronsetup_options.cron_setup_id", "=", "cron_setups.id")
                        ->join("cron_settings", "cron_setups.user_id", "=", "cron_settings.user_id")
                        ->join("users", "users.id", "=", "cron_setups.user_id")#sender
                        ->join("smtps", "smtps.user_id", "=", "cron_setups.user_id")
                        ->where("cron_setups.id", $crontime->cronsetup_id)->first();

        # Default Template
        $eph = DB::table("email_placeholders")->get();

        #Receiver details
        $receiver = DB::table("users")->select("ID",DB::raw("CONCAT(first_name, ' ', last_name) AS fullName"),"language_id" ,"first_name", "last_name", "cron_email", "user_status")->where("id", $admin->selected_user)->first();

        #analyses info
        $analyses = DB::table("cronsetup_analyses")
                        ->where("start_time", $crontime->start_time)
                        ->where("cron_setup_id", $crontime->cronsetup_id)->first();
        $lang = 'de';
        if($receiver->language_id != '2') $lang = DB::table('languages')->find($receiver->language_id)->short_code;
        setServerLocal($lang);

        $tableName = ($lang != 'de')? $lang.'_email_templates' : 'email_templates';
        $adminTemplate = DB::table($tableName)->where('cron_setting_id',$admin->cronsettingid)->first();
        ### QUERY END ###

        #reciver info
        $receiver_email = $admin->receiver_email ?? $receiver->cron_email;
        $reciver_name = $receiver->fullName;

        #smtp configuration
        $smtp_config = $this->smtpConfig($admin, $admin->fullName);

        #PDF link setup
        $pdf_status = false;
        $pdflink = "";
        // $click = ($admin->language_id == 2) ? "Klick hier" : "Click Here";
        $click = trans('action.click_here');

        if($admin->pdf_export == 1)
        {
            $pdflink = url('/treatment/view_pdf',[$admin->selected_user, $crontime->cronsetup_id, $crontime->id, $admin->unique_id]);
            $pdf_status = true;
        }

        #array in for checking condition
        $others = [
            'start_time' => $crontime->start_time,
            'end_time' => $crontime->end_time,
            'cronsetup_id' => $crontime->cronsetup_id,
            'crontime_id' => $crontime->id,
            'click' => $click,
            'pdf_link' => $pdflink
        ];

        #emailtemplate setup
        $emailTemplate = $this->EmailContentTemplate($receiver, $adminTemplate, $eph, $lang);

        #template structure converated
        $templateStucture = $this->mailTamplate($emailTemplate['temp_body'], $receiver, $admin, $others);

        #mail template array
        $mailTamplate = [
            'mail'          => true,
            'mail_subject'  => $emailTemplate['temp_title'],
            'mail_body'     => $templateStucture,
            "pdf_status"    => $pdf_status,
            "pdf"           => $pdflink,
            'click'         => $click,
            'mail_footer'   => $emailTemplate['temp_footer']
        ];

        $recipient['mail'] = $receiver_email;
        $recipient['name'] = $reciver_name;
        $recipient['lang_id'] = $receiver->language_id;
        $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate,$lang);

        activityLogTrack(4,'resend email to '.$receiver->fullName, 'E-Mail an '.$receiver->fullName.' erneut versendet', 'Remote Treatment', 'remote_treatment');#AL

        return response()->json($mailsend_status);
    }

    /**
     * Do prepare the SMTP Confif
     * @param array $adminsmtp , @param string $sender_name
     * @return array
     */
    protected function smtpConfig($adminsmtp, $sender_name)
	{
        if (env('APP_ENV') !== 'production' || $adminsmtp->smtp_email == null || $adminsmtp->smtp_user_name == null || $adminsmtp->smtp_password == null || $adminsmtp->smtp_port == null || $adminsmtp->smtp_host == null) {
            return [
                'smtp_host'       => env('MAIL_HOST'),
                'smtp_port'       => env('MAIL_PORT'),
                'smtp_username'   => env('MAIL_USERNAME'),
                'smtp_password'   => env('MAIL_PASSWORD'),
                'smtp_encryption' => env('MAIL_ENCRYPTION'),
                'from_email'      => env('MAIL_FROM_ADDRESS'),
                'from_name'       => env('MAIL_FROM_NAME')
            ];
        }
        #smtp config
        if($adminsmtp->smtp_host == "ssl://smtp.gmail.com" || $adminsmtp->smtp_host == "tls://smtp.gmail.com")
        {
            $tmphost  = explode("://", $adminsmtp->smtp_host);
            $smtphost = $tmphost[1];
        }else
            $smtphost = $adminsmtp->smtp_host;

        if($adminsmtp->smtp_ssl == "ssl://smtp.gmail.com" || $adminsmtp->smtp_ssl == "tls://smtp.gmail.com")
        {
            $tmpencryp  = explode("://", $adminsmtp->smtp_ssl);
            $encryption = $tmpencryp[0];
        }
        else
            $encryption = $adminsmtp->smtp_ssl;

        $smtpconfig = [

            'smtp_host'       => $smtphost,
            'smtp_port'       => $adminsmtp->smtp_port,
            'smtp_username'   => trim($adminsmtp->smtp_user_name),
            'smtp_password'   => $adminsmtp->smtp_password,
            'smtp_encryption' => $encryption,
            'from_email'      => $adminsmtp->smtp_email,
            'from_name'       => ($adminsmtp->sender_name == "") ? $sender_name : $adminsmtp->sender_name
        ];

        return $smtpconfig;
    }

    /**
     * Do prepare the mail tempaltes
     * @return mailFormet
     */
    protected function mailTamplate($string, $subuser, $admin, $others){

        if ($subuser->language_id == 2) {
            $overviewText = "Übersicht";
        }
        else {
            $overviewText = "Overview";
        }

        $variables = array(
            '{first_name}' => $subuser->first_name,
            '{last_name}' => $subuser->last_name,
            '{start_date_time}' => $others['start_time'],
            '{end_date_time}' => $others['end_time'],
            '{link}' => ($admin->customer_link == 1) ? "<a href='".url('cron/open_treat',[$others['cronsetup_id'],$others['crontime_id'],$admin->unique_id])."'><b>".$others['click']."</b></a>" : "",
            '{client_note}' => $others['client_note'],
            '{pdf_link}' => '<a href="'.$others['pdf_link'].'"><b>'.$others['click'].'</b></a>',
            '{overview_link}' => "<a href='".url('cron/overview/' . $others['cronsetup_id'] . '/' . rand()) . "'><b>' . $overviewText . '</b></a>"
        );

        // Debug logging - remove this after fixing the issue
        if (strpos($string, 'Fernbehandlungen') !== false) {
            \Log::info('MailSendService template processing:', [
                'template' => $string,
                'overview_link' => $variables['{overview_link}'],
                'cronsetup_id' => $others['cronsetup_id'] ?? 'missing'
            ]);
        }

        $datas = strtr($string, $variables);
        return $datas;
    }

    /**
     * Do prepare the mail tempalte for resend
     * @return mailFormet
     */
    protected function EmailContentTemplate($receiver,$template,$defaultTemp,$lang){
        if($template){
            $emailTemplate = [
                'temp_title' => strip_tags(($template->email_template_title_resend == '') ? $defaultTemp->where('type','email_template_title_resend')->where('language',$lang)->first()->message : $template->email_template_title_resend),
                'temp_body' => ($template->email_template_resend == '') ? $defaultTemp->where('type','email_template_resend')->where('language',$lang)->first()->message : $template->email_template_resend,
                'temp_footer' => ($template->email_template_footer == '') ? $defaultTemp->where('type','global_footer_'.$lang)->first()->message : $template->email_template_footer
            ];
        }else{
            $emailTemplate = [
                'temp_title' => strip_tags($defaultTemp->where('type','email_template_title_resend')->where('language',$lang)->first()->message),
                'temp_body' => $defaultTemp->where('type','email_template_resend')->where('language',$lang)->first()->message,
                'temp_footer' => $defaultTemp->where('type','global_footer_'.$lang)->first()->message
            ];
        }
        return $emailTemplate;

    }

    /**
     * Do prepare the mail tempalte for cronMail
     * @return mailFormet
     */
    public function EmailTemplate($admintemp, $analyses, $mailType, $template, $eph, $lang)
	{
		#email template
        if($mailType === 2){
        // if(($others['end_diff'] > 0 && $others['end_diff'] <= 120) && ($others['status'] == 0 || $others['status'] == 1) && (($admintemp->due_power == 0 && $admintemp->end_email == 1) || ($admintemp->due_power == 1 && $admintemp->end_email_duepower == 1))){

            #END email greeting
            $emailTemplate = [
                'temp_title' => $template->email_template_title_stop ?? $eph->where('type','end_treatment_title_'.$lang)->first()->message,
                'temp_body' => $template->email_template_stop ?? $eph->where('type','end_treatment_'.$lang)->first()->message,
            ];
        }
        else if($mailType === 0){
        // else if(!$analyses){
            #red day
        	$emailTemplate = [
                'temp_title' => $template->email_template_title_blank ?? $eph->where('type','blank_treatment_title_'.$lang)->first()->message,
                'temp_body' =>  $template->email_template_blank ?? $eph->where('type','blank_treatment_'.$lang)->first()->message
            ];
        }
        else if($mailType === 1){
        // else if(($others['diff'] > 0 && $others['diff'] <= $others['before']) && $others['status'] == 0 && (($admintemp->due_power == 0 && $admintemp->start_email == 1) || ($admintemp->due_power == 1 && $admintemp->start_email_duepower == 1))){

            #Strrt Email with cron link
            $emailTemplate = [
                'temp_body' => $template->email_template_title_stop ?? $eph->where('type','start_treatment_title_'.$lang)->first()->message,
                'temp_body' => $template->email_template_stop ?? $eph->where('type','start_treatment_'.$lang)->first()->message
            ];

        }
        $emailTemplate['temp_footer'] = $template->email_template_footer ?? $eph->where('type','global_footer_'.$lang)->first()->message;

        return $emailTemplate;
	}

    /**
     * Do Mail send
     * @param config, @param recipient, @param language
     * @return array
     */

    public function sendMail($config, $recipient, $template, $lang)
    {
        $mailService = new MailService();

        // Use the generic send method
        return $mailService->send(
            $config,
            $recipient,
            $template,
            $lang
        );
    }
}
