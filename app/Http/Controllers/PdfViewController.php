<?php

namespace App\Http\Controllers;

use App\Model\User;
use App\Services\Mail\MailService;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\Datatables\Datatables;

class PdfViewController extends Controller
{
    public function pdfView()
    {
        return view("Frontend.pdfView.pdf_view");
    }

    public function getPdfData(Request $request)
    {
        $pdf_data = DB::table('all_pdfs');
        if (isset($request->user)) $pdf_data->where("fuser_id", $request->user);
        elseif ($request->isClearClicked) $pdf_data->where("creator_id", getAuthId());
        elseif (getUserId() != getAuthId()) $pdf_data->where("fuser_id", getUserId());
        elseif (Auth::user()->user_type == 0) $pdf_data->where("fuser_id", getUserId());
        else $pdf_data->where("creator_id", getUserId());

        if ($request->order[0]['column'] == 1) $pdf_data->orderBy('name_of_pdf', $request->order[0]['dir']);
        else if ($request->order[0]['column'] == 2) $pdf_data->orderBy(DB::raw("DATE_FORMAT(created_at, '%Y-%m-%d')"), $request->order[0]['dir']);

        return Datatables::of($pdf_data)->addIndexColumn()->addColumn('action', function ($row) {
            $btns = '<p class="text-success cursor-pointer" style="max-width: fit-content;margin-bottom: 2px;" id="resend-btn" onclick="pdfResendMail(' . $row->pdf_id . ',\'' . $row->pdf_type . '\')"><i class="fas fa-share">&nbsp;</i><span class="hide-table-btn-text">' . trans("action.resend_btn") . '</span></p><p class="text-primary cursor-pointer view-btn" style="max-width: fit-content;margin-bottom: 2px;" data-id="' . $row->pdf_id . '" data-type="' . $row->pdf_type . '"><i class="fas fa-eye">&nbsp;</i><span class="hide-table-btn-text">' . trans("action.view") . '</span></p><p class="text-danger cursor-pointer" style="max-width: fit-content;margin-bottom: 2px;" id="delete-btn" onclick="deletePdf(' . $row->pdf_id . ',\'' . $row->pdf_type . '\')" data-type="' . $row->pdf_type . '" data-id="' . $row->pdf_id . '"><i class="fas fa-trash-alt">&nbsp;</i><span class="hide-table-btn-text">' . trans("action.delete") . '</span></p>';
            return $btns;
        })->addColumn('id', function ($row) {
            return $row->pdf_id;
        })->addColumn('created_at', function ($row) {
            $date = date_change_with_time($row->created_at);
            return $date;
        })->addColumn('pdf_type', function ($row) {
            $type_of_pdf = ($row->pdf_type == "Normal") ? "DC" : "Efit";
            return $type_of_pdf;
        })->rawColumns(['action', 'created_at'])->make(true);
    }

    public function emailTemplateSettings()
    {
        $userId = getAuthID();
        $userLang = app()->getLocale() ?? 'de';
        $table = ($userLang == 'de') ? 'email_templates' : $userLang . '_email_templates';

        $cron_settings_data = DB::table('cron_settings')->select("id")->where("user_id", $userId)->first();
        $email_temp_data = DB::table($table)->select("resend_mail_title", "resend_mail_msg")->where("cron_setting_id", $cron_settings_data->id)->first();

        if (($email_temp_data->resend_mail_title != null) && ($email_temp_data->resend_mail_msg != null)) {
            $mail_title = $email_temp_data->resend_mail_title;
            $mail_msg = $email_temp_data->resend_mail_msg;
            $languages = DB::table('languages')->where('status', true)->orWhere('id', 2)->select("id", "name", "short_code")->get();

            return view("Frontend.pdfView.pdf_view_mail_temp", compact("mail_title", "mail_msg", "languages"));
        } else {
            $mail_title_type = ($userLang == 'de') ? 'resend_mail_title_de' : 'resend_mail_title_' . $userLang;
            $mail_msg_type = ($userLang == 'de') ? 'resend_mail_msg_de' : 'resend_mail_msg_' . $userLang;

            $email_placeholder_title = DB::table('email_placeholders')->select("message")->where("type", $mail_title_type)->first();
            $email_placeholder_msg = DB::table('email_placeholders')->select("message")->where("type", $mail_msg_type)->first();

            $mail_title = $email_placeholder_title->message ?? '';
            $mail_msg = $email_placeholder_msg->message ?? '';
            $languages = DB::table('languages')->where('status', true)->orWhere('id', 2)->select("id", "name", "short_code")->get();

            return view("Frontend.pdfView.pdf_view_mail_temp", compact("mail_title", "mail_msg", "languages"));
        }
    }

    public function getMailTemplate(Request $request)
    {
        $userId = getAuthID();
        $userLang = $request->langCode;
        $table = ($userLang == 'de') ? 'email_templates' : $userLang . '_email_templates';

        $cron_settings_data = DB::table('cron_settings')->select("id")->where("user_id", $userId)->first();
        $email_temp_data = DB::table($table)->select("resend_mail_title", "resend_mail_msg")->where("cron_setting_id", $cron_settings_data->id)->first();

        if (($email_temp_data->resend_mail_title != null) && ($email_temp_data->resend_mail_msg != null)) {
            $mail_title = $email_temp_data->resend_mail_title;
            $mail_msg = $email_temp_data->resend_mail_msg;
            $languages = DB::table('languages')->select("id", "name", "short_code")->get();

            return response()->json([
                'success' => true,
                'title' => $mail_title ?? '',
                'msg' => $mail_msg ?? ''
            ]);
        } else {
            $mail_title_type = ($userLang == 'de') ? 'resend_mail_title_de' : 'resend_mail_title_' . $userLang;
            $mail_msg_type = ($userLang == 'de') ? 'resend_mail_msg_de' : 'resend_mail_msg_' . $userLang;

            $email_placeholder_title = DB::table('email_placeholders')->select("message")->where("type", $mail_title_type)->first();
            $email_placeholder_msg = DB::table('email_placeholders')->select("message")->where("type", $mail_msg_type)->first();

            $mail_title = $email_placeholder_title->message ?? '';
            $mail_msg = $email_placeholder_msg->message ?? '';
            $languages = DB::table('languages')->select("id", "name", "short_code")->get();

            return response()->json([
                'success' => true,
                'title' => $mail_title ?? '',
                'msg' => $mail_msg ?? ''
            ]);
        }
    }

    public function storeResendMailTemplate(Request $request)
    {
        $userId = getAuthID();
        $lang_code = $request->lang_code;

        $email_data['resend_mail_title'] = $request->mail_title;
        $email_data['resend_mail_msg'] = $request->mail_msg;


        $cron_settings_data = DB::table('cron_settings')->select("id")->where("user_id", $userId)->first();
        $table = ($lang_code == 'de') ? 'email_templates' : $lang_code . '_email_templates';

        $email_temp_data = DB::table($table)->where("cron_setting_id", $cron_settings_data->id);

        if (!$email_temp_data->exists()) {
            $email_data['cron_setting_id'] = $cron_settings_data->id;
            $email_temp_data->insert($email_data);
        } else {
            $email_temp_data->update($email_data);
        }

        return response()->json([
            'success' => true,
            'message' => trans('action.update_successfully')
        ]);
    }

    // resend mail
    public function pdfResendMail(Request $request)
    {
        $userId = getAuthID();
        $userLang = app()->getLocale() ?? 'de';

        $table_name = ($userLang == 'de') ? 'email_templates' : $userLang . '_email_templates';

        $cron_settings_data = DB::table('cron_settings')->select("id")->where("user_id", $userId)->first();
        $email_temp_data = DB::table($table_name)->where("cron_setting_id", $cron_settings_data->id)->first();

        $pdflink = url("/view_pdf/$request->pdf_type/$request->pdf_id");

        $admin = DB::table('users')->select('users.*', DB::raw("CONCAT(first_name, ' ', last_name) AS fullName"), 'smtps.*')
            ->join("smtps", "smtps.user_id", "=", "users.id")
            ->where("users.id", $userId)
            ->first();

        if ($request->pdf_type == "Normal") {
            $sourcePdfRecord = DB::table('focus_sessionpdf')->find($request->pdf_id);
        } else {
            $sourcePdfRecord = DB::table('pdfs')->find($request->pdf_id);
        }
        $receiverId = ($sourcePdfRecord->subuser_id != 0) ? $sourcePdfRecord->subuser_id : $sourcePdfRecord->user_id;
        #reciver info
        $receiver = DB::table("users")->select("id", "first_name", "last_name", DB::raw("CONCAT(first_name, ' ', last_name) AS fullName"), "language_id", "email", "cron_email")->where("id", $receiverId)->first();

        $receiver_email = ($receiver->cron_email != null) ? $receiver->cron_email : $receiver->email;
        $receiver_name = $receiver->fullName;
        

        #smtp configuration
        $smtp_config = $this->smtpConfig($admin, $admin->fullName);

        if (($email_temp_data->resend_mail_title != null) && ($email_temp_data->resend_mail_msg != null)) {
            $mail_title = $email_temp_data->resend_mail_title;
            $mail_msg = $email_temp_data->resend_mail_msg;
            $mail_msg_footer = $email_temp_data->email_template_footer ?? '';
        } else {
            $mail_title_type = ($userLang == 'de') ? 'resend_mail_title_de' : 'resend_mail_title_' . $userLang;
            $mail_msg_type = ($userLang == 'de') ? 'resend_mail_msg_de' : 'resend_mail_msg_' . $userLang;
            $mail_footer_type = ($userLang == 'de') ? 'global_footer_de' : 'global_footer_' . $userLang;

            $email_placeholder_title = DB::table('email_placeholders')->select("message")->where("type", $mail_title_type)->first();
            $email_placeholder_msg = DB::table('email_placeholders')->select("message")->where("type", $mail_msg_type)->first();
            $email_placeholder_footer = DB::table('email_placeholders')->select("message")->where("type", $mail_footer_type)->first();

            $mail_title = $email_placeholder_title->message ?? '';
            $mail_msg = $email_placeholder_msg->message ?? '';
            $mail_msg_footer = $email_placeholder_footer->message ?? '';
        }

        // Try to find a cronsetup_id for this user to generate overview link
        $cronsetup = DB::table('cron_setups')
            ->where('selected_user', $receiverId)
            ->where('is_deleted', 0)
            ->orderBy('id', 'desc')
            ->first();

        if ($cronsetup) {
            // Store cronsetup_id in request for mailTamplate method
            request()->merge(['cronsetup_id' => $cronsetup->id]);
        }

        $templateStucture = $this->mailTamplate($mail_msg, $receiver, $pdflink, $mail_msg_footer);
        #mail template array
        $mailTemplate = [
            'mail' => true,
            'mail_subject' => $mail_title,
            'mail_body' => $templateStucture,
        ];
        $language_short_core = DB::table('languages')->where('id', $receiver->language_id)->first(['short_code'])->short_code ?? 'de';
        $recipient['mail'] = $receiver_email;
        $recipient['name'] = $receiver_name;
        $recipient['lang_id'] = $receiver->language_id;
        $recipient_lang_code = $language_short_core;
        $mail_send_status = $this->sendMail($smtp_config, $recipient, $mailTemplate, $recipient_lang_code);

        return response()->json($mail_send_status);
    }

    // main send
    public function sendMail($config, $recipient, $template, $lang)
    {
        // Create instance of MailService
        $mailService = new MailService();

        // Use the service to send the email
        return $mailService->send(
            $config,
            $recipient,
            $template,
            $lang
        );
    }

    // smtp config
    public function smtpConfig($adminsmtp, $sender_name)
    {
        if (
            env('APP_ENV') !== 'production' ||
            $adminsmtp == null ||
            $adminsmtp->smtp_email == null ||
            $adminsmtp->smtp_user_name == null ||
            $adminsmtp->smtp_password == null ||
            $adminsmtp->smtp_port == null ||
            $adminsmtp->smtp_host == null
        ) {
            return [
                'smtp_host' => env('MAIL_HOST'),
                'smtp_port' => env('MAIL_PORT'),
                'smtp_username' => env('MAIL_USERNAME'),
                'smtp_password' => env('MAIL_PASSWORD'),
                'smtp_encryption' => env('MAIL_ENCRYPTION'),
                'from_email' => env('MAIL_FROM_ADDRESS'),
                'from_name' => env('MAIL_FROM_NAME')
            ];
        }
        #smtp config
        if ($adminsmtp->smtp_host == "ssl://smtp.gmail.com" || $adminsmtp->smtp_host == "tls://smtp.gmail.com") {
            $tmphost = explode("://", $adminsmtp->smtp_host);
            $smtphost = $tmphost[1];
        } else
            $smtphost = $adminsmtp->smtp_host;

        if ($adminsmtp->smtp_ssl == "ssl://smtp.gmail.com" || $adminsmtp->smtp_ssl == "tls://smtp.gmail.com") {
            $tmpencryp = explode("://", $adminsmtp->smtp_ssl);
            $encryption = $tmpencryp[0];
        } else
            $encryption = $adminsmtp->smtp_ssl;

        $smtpconfig = [

            'smtp_host' => $smtphost,
            'smtp_port' => $adminsmtp->smtp_port,
            'smtp_username' => trim($adminsmtp->smtp_user_name),
            'smtp_password' => $adminsmtp->smtp_password,
            'smtp_encryption' => $encryption,
            'from_email' => $adminsmtp->smtp_email,
            'from_name' => ($adminsmtp->sender_name == "") ? $sender_name : $adminsmtp->sender_name
        ];

        return $smtpconfig;
    }

    // Delete pdf
    public function deletePdf()
    {
        $pdf_type = request()->pdf_type;
        $pdf_id = request()->id;

        if (isset($pdf_id)) {
            if ($pdf_type == "Normal") {
                DB::table('focus_sessionpdf')->where('id', $pdf_id)->delete();
            } else {
                DB::table('pdfs')->where('id', $pdf_id)->delete();
            }
        }

        return response()->json([
            'success' => true,
            'message' => trans('action.pdf_deleted')
        ]);
    }

    // Delete all pdf
    public function deleteAllPdf()
    {
        $pdf_ids = request()->ids;

        if (is_array($pdf_ids) && !empty($pdf_ids)) {
            foreach ($pdf_ids as $key => $id) {
                if ($id[1] == "DC") {
                    DB::table('focus_sessionpdf')->where('id', $id[0])->delete();
                } else {
                    DB::table('pdfs')->where('id', $id[0])->delete();
                }
            }
        }

        return response()->json([
            'success' => true,
            'message' => trans('action.pdf_deleted')
        ]);
    }

    public function viewPdf($type, $id)
    {
        if ($type == "Normal") {
            $pdf_data = DB::table('focus_sessionpdf')->find($id);
            $data = $pdf_data->pdfunique;
        } else {
            $pdf_data = DB::table('pdfs')->find($id);
            $data = $pdf_data->pdf;
        }

        return view("Frontend.pdfView.show_pdf", compact("data", "type"));
    }

    public function authSubUsers(Request $req)
    {
        $users = User::where('boss_id', getAuthId());

        if (isset($req->term) && $req->term != "") {
            $users->where(DB::raw("CONCAT(first_name, ' ', last_name) "), 'LIKE', '%' . $req->term . '%');
        }

        return response()->json([
            'success' => true,
            'users' => $users->get(['id', 'first_name', 'last_name'])
        ]);
    }

    protected function mailTamplate($mail_msg, $receiver, $pdflink, $footer)
    {
        // Determine overview text based on language
        if ($receiver->language_id == 2) {
            $overviewText = "Übersicht";
        } else {
            $overviewText = "Overview";
        }

        $variables = array(
            '{first_name}' => $receiver->first_name,
            '{last_name}' => $receiver->last_name,
            '{pdf_link}' => '<p>' . ' <a href="' . $pdflink . '">' . trans('action.click_here') . '</a></p>',
            '{footer}' => $footer ?? '',
            '{start_date_time}' => '', // Add default empty values for missing data
            '{end_date_time}' => '',
            '{link}' => '',
            '{client_note}' => '',
            '{remote_id}' => '',
            '{overview_link}' => '', // Add empty overview_link as fallback
        );

        // Try to get cronsetup_id from multiple sources
        $cronsetup_id = null;

        // Check request parameters
        if (request()->has('cronsetup_id')) {
            $cronsetup_id = request()->get('cronsetup_id');
        }
        // Check if it's in the URL path
        elseif (request()->route('cronsetup_id')) {
            $cronsetup_id = request()->route('cronsetup_id');
        }
        // Check session or other sources
        elseif (session()->has('current_cronsetup_id')) {
            $cronsetup_id = session()->get('current_cronsetup_id');
        }

        // Generate overview link if cronsetup_id is available
        if ($cronsetup_id) {
            $variables['{overview_link}'] = "<a href='" . url('cron/overview/' . $cronsetup_id . '/' . rand()) . "'><b>" . $overviewText . "</b></a>";
        }

        return strtr($mail_msg, $variables);
    }
}
