<?php

class CronMail{

    public $db_con;
    public $site_link;

    public function __construct()
    {
        include_once 'env.php';
        #db connection
        $this->db_con = mysqli_connect(getenv('DB_HOST'), getenv('DB_USERNAME'), getenv('DB_PASSWORD'), getenv('DB_DATABASE'));
        #sitelink
        $this->site_link = getenv('APP_URL');
    }

    public function index()
    {
        $this->cronMailSend();
    }

    public function cronMailSend()
    {
        #default_timezone
        date_default_timezone_set('Europe/Vienna');

        $time1 = 3600;
		$dt1 = date("Y-m-d H:i:s");
		$dt2 = date("Y-m-d H:i:s", strtotime($dt1) + $time1);

		#crontimes
        $crontime = "SELECT * FROM cronsetup_times WHERE start_time >= '".$dt1."' and start_time <= '".$dt2."' or end_time >= '".$dt1."' and end_time <= '".$dt2."' and status != 2";
        $crontime_query = mysqli_query($this->db_con, $crontime);

        #email placeholder
        $eph = "SELECT id, message FROM email_placeholders";
        $eph_query = mysqli_query($this->db_con, $eph);
        $eph_row = mysqli_fetch_all($eph_query, MYSQLI_ASSOC);

        $mailreport  = array();
        $send_status = false;

        while($timerow = mysqli_fetch_object($crontime_query))
        {
            $time_start = microtime(true);

            ### QUERY START ###

            #alltimes
            $crontimes = "SELECT id, end_time FROM cronsetup_times WHERE cronsetup_id = ".$timerow->cronsetup_id;
            $crontimes_query = mysqli_query($this->db_con, $crontimes);
            $crontimes_row   = mysqli_fetch_all($crontimes_query, MYSQLI_ASSOC);
            $timescnt  = mysqli_num_rows($crontimes_query);


            #admin info
            $admin_info  = "SELECT cron_setups.*, cronsetup_options.*,
                            cron_settings.id, cron_settings.send_email_time,
                            users.first_name, users.last_name, users.cron_email, users.user_status, users.language_id,
                            smtps.*, email_templates.* FROM cron_setups
                            LEFT JOIN cronsetup_options ON cronsetup_options.cron_setup_id = cron_setups.id
                            LEFT JOIN cron_settings ON cron_settings.user_id = cron_setups.user_id
                            LEFT JOIN users ON users.id = cron_setups.user_id
                            LEFT JOIN smtps ON smtps.user_id = cron_setups.user_id
                            LEFT JOIN email_templates ON email_templates.cron_setting_id = cron_settings.id
                            WHERE cron_setups.id = ".$timerow->cronsetup_id;

            $admin_query = mysqli_query($this->db_con, $admin_info);
            $admin_row = mysqli_fetch_object($admin_query);

            #subuser info
            $subuser  = "SELECT first_name, last_name, cron_email, user_status  FROM users WHERE id = ".$admin_row->selected_user;
            $subuser_query = mysqli_query($this->db_con, $subuser);
            $subuser_row = mysqli_fetch_object($subuser_query);

            #analyses info
            $analyses = "SELECT id, cron_setup_id, start_time  FROM cronsetup_analyses WHERE start_time = '".$timerow->start_time."' and cron_setup_id = ".$timerow->cronsetup_id;
            $analyses_query =  mysqli_query($this->db_con, $analyses);
            $analyses_row = mysqli_fetch_assoc($analyses_query);

            ### QUERY END ###

            #before send time
	        $beforesend = 300;
	        if($admin_row->send_email_time != 0)
                $beforesend = $admin_row->send_email_time*60;

            #time info
	        $start_time = strtotime($timerow->start_time);
            $end_time   = $crontimes_row[$timescnt-1]['end_time'];
            $nowtime    = strtotime(date('Y-m-d H:i:s'));
            $endtime    = strtotime($end_time);
	        $start_diff = $start_time - $nowtime;
            $end_diff   = $endtime - $nowtime;



            #conditions to ignore sendmail
            if($admin_row->user_id != 4402) continue;

            #debug
            echo '<pre>';
            print_r($timerow);
            print_r($admin_row);
            print_r($start_diff);
            echo '<br>';
            print_r($beforesend);


            if($start_diff > $beforesend) continue;
            if($start_diff < 0 && $end_diff > 120) continue;
            if($admin_row->user_status == 0 || $subuser_row->user_status == 0) continue;
            if($admin_row->is_deleted == 1 || $admin_row->as_draft == 1 || $admin_row->is_stop == 1) continue;


            die("before con");
            #reciver info
            $sender_name = $admin_row->first_name." ".$admin_row->last_name;
			$receiver_email = ($admin_row->email == null)? $subuser_row->cron_email : $admin_row->email;
            $reciver_name = $subuser_row->first_name." ". $subuser_row->last_name;

            #smtp configuration
            $smtp_config = $this->smtpConfig($admin_row, $sender_name);

            #PDF link setup
	        $pdf_status = false;
            $pdflink = "";
            $pdfmsg = "";
            $click = ($admin_row->language_id == 2) ? "Klick hier" : "Click Here";

            if($admin_row->pdf_export == 1)
	        {
	        	$pdflink = $this->site_link.'/treatment/view_pdf/'.$admin_row->selected_user.'/'.$timerow->cronsetup_id.'/'.$timerow->id.'/'.$admin_row->unique_id;
                $pdf_status = true;

                if($admin_row->language_id == 2)
                    $pdfmsg = "Hier finden Sie den PDF-Anhang";
                else
                    $pdfmsg = "Please Find PDF Attachment";
            }


            if(($end_diff > 0 && $end_diff < 120) || !$analyses_row) $pdf_status = false;


            #array in for checking condition
            $others = [
                'diff' => $start_diff,
                'end_diff' => $end_diff,
                'before' => $beforesend,
                'start_time' => $timerow->start_time,
                'end_time' => $timerow->end_time,
                'nowtime' => $nowtime,
                'status' => $timerow->status,
                'cronsetup_id' => $timerow->cronsetup_id,
                'crontime_id' => $timerow->id,
                'client_note' => $admin_row->client_note,
                'click' => $click
            ];

            #emailtemplate setup
            $emailTemplate = $this->EmailTemplate($admin_row, $analyses_row, $others, $eph_row);

            #template structure converated
            if(!empty($emailTemplate))
                $templateStucture = $this->mailTamplates($emailTemplate['temp_body'], $subuser_row, $admin_row, $others);

            #mail template array
            if(!empty($emailTemplate))
                $mailTamplate = ['mail' => true, 'mail_subject' => $emailTemplate['temp_title'], 'mail_body' => $templateStucture, "pdf_status" => $pdf_status, "pdf" => $pdflink, "pdfmsg" => $pdfmsg, "click" => $click];

            #debug
            echo '<pre>';
            print_r($timerow);
            print_r($admin_row);
            print_r($start_diff);
            print_r($emailTemplate);
            print_r($mailTamplate);
            die("break");

            if($receiver_email != "" && ($start_diff > 0 && $start_diff <= $beforesend) && $timerow->status == 0 && (($admin_row->due_power == 0 && $admin_row->start_email == 1) || ($admin_row->due_power == 1 && $admin_row->start_email_duepower == 1))){

                $recipient['mail'] = $receiver_email;
                $recipient['name'] = $reciver_name;
                $datetime = date("Y-m-d H:i:s");

                $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate);

                $time_end = microtime(true);
                $ex_time = ($time_end - $time_start)/60;
                $runtime = round($ex_time, 3);

                $mailstatus = 1;
                if($crontimes_row[$timescnt-1]['id'] != $timerow->id) $mailstatus = 2;

                #updating cronsetuptime/treatment_time status
                $update_status = "UPDATE cronsetup_times SET status = $mailstatus where id = ".$timerow->id;
                $query = mysqli_query($this->db_con, $update_status);

                if($mailsend_status['status'] == "" || $mailsend_status['status'] == false) $mailsend_status['status'] = 0;

                #cronmail record
                $mailreport[] = "('" . $admin_row->user_id . "', '" . $admin_row->selected_user . "', '" . $receiver_email . "', '" . $runtime . "', '" . $mailsend_status['status'] . "', '" . $mailsend_status['msg'] . "', '" . 0 . "', '" . $datetime . "')";

                $send_status = true;

            }else if($receiver_email != "" && ($end_diff > 0 && $end_diff <= 120) && ($timerow->status == 0 || $timerow->status == 1) && (($admin_row->due_power == 0 && $admin_row->end_email == 1) || ($admin_row->due_power == 1 && $admin_row->end_email_duepower == 1))){

                $recipient['mail'] = $receiver_email;
                $recipient['name'] = $reciver_name;
                $datetime = date("Y-m-d H:i:s");

                $mailsend_status = $this->sendMail($smtp_config, $recipient, $mailTamplate);

                $time_end = microtime(true);
                $ex_time = ($time_end - $time_start)/60;
                $runtime = round($ex_time, 3);

                #updating cronsetuptime/treatment_time status
                $update_status = "UPDATE cronsetup_times SET status = 2 where id = ".$timerow->id;
                $query = mysqli_query($this->db_con, $update_status);

                if($mailsend_status['status'] == "" || $mailsend_status['status'] == false) $mailsend_status['status'] = 0;

                #cronmail record
                $mailreport[] = "('" . $admin_row->user_id . "', '" . $admin_row->selected_user . "', '" . $receiver_email . "', '" . $runtime . "', '" . $mailsend_status['status'] . "', '" . $mailsend_status['msg'] . "', '" . 1 . "', '" . $datetime . "')";

                $send_status = true;
            }
        }

        if($send_status == true)
        {
            #debug
            echo '<pre>';
            print_r($admin_row);
            print_r($mailreport);

            $record = "INSERT INTO cronmail_record(admin_id, user_id, user_email, execution_time, status, mail_response, mail_type, created_at) VALUES ".implode(",", $mailreport);
            $record_query = mysqli_query($this->db_con, $record);
            die("ok");
        }
    }


    public function sendMail($config, $recipient, $template)
    {
        require_once('vendor/autoload.php');

        // Subject encoded with base64 for UTF-8 compatibility
        $mail_subject = '=?UTF-8?B?'.base64_encode($template['mail_subject']).'?=';

        try {
            // Configure the transport with DSN
            $dsn = sprintf(
                '%s://%s:%s@%s:%d',
                $config['smtp_encryption'] === 'ssl' ? 'smtps' : 'smtp',
                $config['smtp_username'],
                $config['smtp_password'],
                $config['smtp_host'],
                $config['smtp_port']
            );

            $transport = \Symfony\Component\Mailer\Transport::fromDsn($dsn);
            $mailer = new \Symfony\Component\Mailer\Mailer($transport);

            // Create the email
            $email = new \Symfony\Component\Mime\Email();
            $email->subject($mail_subject);
            $email->from(new \Symfony\Component\Mime\Address($config['from_email'], $config['from_name']));
            $email->to(new \Symfony\Component\Mime\Address($recipient['mail'], $recipient['name']));

            // Set the body based on template settings
            if ($template['pdf_status'] == true) {
                $htmlContent = "<p>" . $template['mail_body'] . "</p>"
                    . "<p>" . $template['pdfmsg'] . " <a href='" . $template['pdf'] . "'><b>" . $template['click'] . "</b></a></p>"
                    . "<strong>energetisch.fit</strong>";
            } else {
                $htmlContent = "<p>" . $template['mail_body'] . "</p>"
                    . "<strong>energetisch.fit</strong>";
            }

            $email->html($htmlContent);

            // Send the email
            $mailer->send($email);

            $response = [
                'status' => true,
                'msg' => "success"
            ];

            return $response;

        } catch (\Exception $ex) {
            return [
                'status' => false,
                'msg' => $ex->getMessage()
            ];
        }
    }


    public function smtpConfig($adminsmtp, $sender_name)
	{
        $smtpconfig = array();

        #smtp config
        if($adminsmtp->smtp_host == "ssl://smtp.gmail.com" || $adminsmtp->smtp_host == "tls://smtp.gmail.com")
        {
                $tmphost  = explode("://", $adminsmtp->smtp_host);
                $smtphost = $tmphost[1];
        }else
                $smtphost = $adminsmtp->smtp_host;

        if($adminsmtp->smtp_ssl == "ssl://smtp.gmail.com" || $adminsmtp->smtp_ssl == "tls://smtp.gmail.com")
        {
                $tmpencryp  = explode("://", $adminsmtp->smtp_ssl);
                $encryption = $tmpencryp[0];
        }
        else
            $encryption = $adminsmtp->smtp_ssl;

        $smtpconfig = [

            'smtp_host'       => $smtphost,
            'smtp_port'       => $adminsmtp->smtp_port,
            'smtp_username'   => trim($adminsmtp->smtp_user_name),
            'smtp_password'   => $adminsmtp->smtp_password,
            'smtp_encryption' => $encryption,
            'from_email'      => $adminsmtp->smtp_email,
            'from_name'       => ($adminsmtp->sender_name == "") ? $sender_name : $adminsmtp->sender_name
        ];

        return $smtpconfig;
    }

    public function EmailTemplate($admintemp, $analyses, $others, $eph)
	{
        $emailTemplate = array();

		#email template
        if(($others['end_diff'] > 0 && $others['end_diff'] <= 120) && ($others['status'] == 0 || $others['status'] == 1) && (($admintemp->due_power == 0 && $admintemp->end_email == 1) || ($admintemp->due_power == 1 && $admintemp->end_email_duepower == 1))){

            #END email greeting
            $emailTemplate = [

            	'temp_title' => ($admintemp->email_template_title_stop == "")? $eph[7]['message']:$admintemp->email_template_title_stop,
                'temp_body' => ($admintemp->email_template_stop == "")? $eph[3]['message']:$admintemp->email_template_stop,
            ];
        }
        else if(!$analyses){

        	$emailTemplate = [

        	    'temp_title' => ($admintemp->email_template_title_blank == "")? $eph[10]['message']:$admintemp->email_template_title_blank,
                'temp_body' => ($admintemp->email_template_blank == "")? $eph[9]['message']:$admintemp->email_template_blank,
            ];
        }
        else if(($others['diff'] > 0 && $others['diff'] <= $others['before']) && $others['status'] == 0 && (($admintemp->due_power == 0 && $admintemp->start_email == 1) || ($admintemp->due_power == 1 && $admintemp->start_email_duepower == 1))){

            #Start Email with cron link
            $emailTemplate = [

                'temp_title' => ($admintemp->email_template_title_start == "")? $eph[5]['message']:$admintemp->email_template_title_start,
                'temp_body' => ($admintemp->email_template_start == "")? $eph[2]['message']:$admintemp->email_template_start,
            ];

        }

        return $emailTemplate;
    }

    public function mailTamplates($string, $subuser, $admin, $others){

        if ($subuser->language_id == 2) {
            $overviewText = "Übersicht";
        }
        else {
            $overviewText = "Overview";
        }

        $variables = array(
            '{first_name}' => $subuser->first_name,
            '{last_name}' => $subuser->last_name,
            '{start_date_time}' => $others['start_time'],
            '{end_date_time}' => $others['end_time'],
            '{link}' => ($admin->customer_link == 1) ? '<a href="'.$this->site_link.'/cron/open_treat/'.$others['cronsetup_id'].'/'.$others['crontime_id'].'/'.$admin->unique_id.'"><b>'.$others['click'].'</b></a>' : "",
            '{client_note}' => $others['client_note'],
            '{pdf_link}' => '<a href="'.$others['pdf_link'].'"><b>'.$others['click'].'</b></a>',
            '{overview_link}' => '<a href="'.$this->site_link.'/cron/overview/'.$others['cronsetup_id'].'/'. rand() .'"><b>' . $overviewText . '</b></a>',
            '{remote_id}' => $others['cronsetup_id']
        );

        $datas = strtr($string, $variables);
        return $datas;
    }
}
